<!-- pages/user/center/center.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/pages/index/images/touXiang.png'}}" mode="aspectFill"></image>
      <view class="user-detail">
        <text class="nickname">{{userInfo.nickName || '微信用户'}}</text>
        <text class="welcome">欢迎您的使用</text>
      </view>
    </view>
  </view>

  <!-- 会员状态区域 -->
  <view class="membership-section">
    <view class="membership-card {{isMember ? 'member-active' : 'non-member'}}">
      <view class="membership-info">
        <image class="member-icon" src="/pages/user/center/images/{{isMember ? 'vip-active' : 'vip-inactive'}}.png" mode="aspectFit"></image>
        <view class="member-detail">
          <text class="member-status">{{membershipStatus}}</text>
          <text wx:if="{{memberExpireTime}}" class="expire-time">{{memberExpireTime}}</text>
          <text wx:if="{{isPermanentMember}}" class="permanent-badge">永久会员</text>
        </view>
      </view>
      
      <!-- 会员权益 -->
      <view wx:if="{{isMember}}" class="member-benefits">
        <text class="benefits-title">会员权益</text>
        <view class="benefits-list">
          <text class="benefit-item" wx:for="{{memberBenefits}}" wx:key="index">• {{item}}</text>
        </view>
      </view>
    </view>

    <!-- 续费会员按钮 -->
    <view class="upgrade-btn-container">
      <button class="upgrade-btn {{isMember ? 'renew' : 'upgrade'}}" bindtap="upgradeMembership">
        <view class="btn-content">
          <text class="btn-text">{{isMember ? '续费会员' : '开通会员'}}</text>
          <view class="btn-glow"></view>
        </view>
      </button>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="redeemCard">
      <image class="menu-icon" src="/pages/user/center/images/card.png" mode="aspectFit"></image>
      <text class="menu-text">兑换卡密</text>
      <image class="arrow-icon" src="/pages/user/center/images/arrow.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="goToOrders">
      <image class="menu-icon" src="/pages/user/center/images/order.png" mode="aspectFit"></image>
      <text class="menu-text">我的订单</text>
      <image class="arrow-icon" src="/pages/user/center/images/arrow.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="goToFeedback">
      <image class="menu-icon" src="/pages/user/center/images/feedback.png" mode="aspectFit"></image>
      <text class="menu-text">意见反馈</text>
      <image class="arrow-icon" src="/pages/user/center/images/arrow.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <image class="menu-icon" src="/pages/user/center/images/about.png" mode="aspectFit"></image>
      <text class="menu-text">关于我们</text>
      <image class="arrow-icon" src="/pages/user/center/images/arrow.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 为底部导航栏预留空间 -->
  <view class="tabbar-placeholder"></view>
</view>

<!-- 卡密兑换弹窗 -->
<view class="modal-overlay {{showRedeemModal ? 'show' : ''}}" bindtap="hideRedeemModal">
  <view class="redeem-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">兑换卡密</text>
      <image class="close-btn" src="/pages/user/center/images/close.png" bindtap="hideRedeemModal" mode="aspectFit"></image>
    </view>
    
    <view class="modal-content">
      <view class="purchase-info">
        <text class="info-title">如何购买卡密？</text>
        <text class="info-text">请联系客服微信：km80048 购买卡密</text>
      </view>
      
      <view class="redeem-form">
        <text class="form-label">请输入卡密：</text>
        <input class="card-input" placeholder="请输入16位卡密" value="{{cardCode}}" bindinput="onCardCodeInput" maxlength="16"></input>
        <button class="redeem-submit-btn" bindtap="submitRedeem" disabled="{{!cardCode}}">立即兑换</button>
      </view>
    </view>
  </view>
</view>

