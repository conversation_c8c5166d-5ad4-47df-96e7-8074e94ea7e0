.container {
  width: 100%;
  min-height: 100vh;
  background-color: #4B8BF5;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.image-container {
  width: 100%;
  padding: 20rpx 0;
  background-color: #4B8BF5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 10rpx;
}

.function-area {
  width: 90%;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.function-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.function-item {
  width: 48%;
  height: 200rpx;
  background: #fff;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.icon {
  font-size: 50rpx;
  margin-bottom: 20rpx;
}

/* 为image类型的icon添加具体的宽高 */
image.icon {
  width: 60rpx;
  height: 60rpx;
}

/* 证件照图标特定大小 */
.function-item image[src*="idPhoto"] {
  width: 66rpx;  /* 60rpx * 1.1 */
  height: 66rpx; /* 60rpx * 1.1 */
}

.function-item text:not(.icon) {
  font-size: 28rpx;
  color: #333;
}

.share-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 10rpx;
}

.share-icon {
  width: 30rpx;
  height: 30rpx;
}

/* 为底部导航栏预留空间 */
.tabbar-placeholder {
  height: calc(48px + env(safe-area-inset-bottom));
}
