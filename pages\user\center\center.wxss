/* pages/user/center/center.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4B8BF5 0%, #6BA3F7 100%);
  padding-top: 40rpx;
}

/* 用户信息区域 */
.user-header {
  padding: 40rpx 30rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-detail {
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.welcome {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 会员状态区域 */
.membership-section {
  margin: 0 30rpx 40rpx;
}

.membership-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.membership-card.member-active {
  background: linear-gradient(135deg, #2C2C2C 0%, #1A1A1A 100%);
  color: white;
}

.membership-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.member-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.member-status {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.expire-time {
  font-size: 24rpx;
  opacity: 0.7;
  display: block;
}

.permanent-badge {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #000;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  margin-top: 8rpx;
  display: inline-block;
}

.member-benefits {
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
  padding-top: 20rpx;
}

.benefits-title {
  font-size: 28rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 15rpx;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.benefit-item {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 续费会员按钮 */
.upgrade-btn-container {
  display: flex;
  justify-content: center;
}

.upgrade-btn {
  position: relative;
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  border: none;
  border-radius: 50rpx;
  padding: 0;
  width: 300rpx;
  height: 80rpx;
  overflow: hidden;
  animation: breathe 2s ease-in-out infinite;
}

.upgrade-btn.renew {
  background: linear-gradient(45deg, #4B8BF5, #6BA3F7);
}

.btn-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.btn-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: glow 3s linear infinite;
  z-index: 1;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 功能菜单区域 */
.menu-section {
  background: white;
  margin: 0 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 卡密兑换弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.redeem-modal {
  background: white;
  border-radius: 20rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.modal-overlay.show .redeem-modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-content {
  padding: 30rpx;
}

.purchase-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666;
}

.redeem-form {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.card-input {
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.card-input:focus {
  border-color: #4B8BF5;
}

.redeem-submit-btn {
  background: #4B8BF5;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.redeem-submit-btn:disabled {
  background: #ccc;
}

.tabbar-placeholder {
  height: calc(48px + env(safe-area-inset-bottom));
}



