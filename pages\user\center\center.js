const app = getApp();

Page({
  data: {
    userInfo: {},
    isMember: false,
    isPermanentMember: false,
    membershipStatus: '非会员',
    memberExpireTime: '',
    memberBenefits: [
      '无限制生成简历PDF',
      '高清简历导出',
      '专属简历模板',
      '优先客服支持'
    ],
    showRedeemModal: false,
    cardCode: ''
  },

  onLoad() {
    // 设置自定义tabbar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }

    this.loadUserInfo();
    this.loadMembershipInfo();
  },

  onShow() {
    // 设置自定义tabbar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }

    this.loadMembershipInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({ userInfo });
  },

  // 加载会员信息
  loadMembershipInfo() {
    const isMember = app.globalData.isMember || false;
    const membershipExpiry = app.globalData.membershipExpiry;
    
    let membershipStatus = '非会员';
    let memberExpireTime = '';
    let isPermanentMember = false;

    if (isMember) {
      if (membershipExpiry === 'permanent') {
        membershipStatus = '永久会员';
        isPermanentMember = true;
      } else if (membershipExpiry) {
        membershipStatus = '会员';
        const expireDate = new Date(membershipExpiry);
        memberExpireTime = `到期时间：${expireDate.getFullYear()}-${(expireDate.getMonth() + 1).toString().padStart(2, '0')}-${expireDate.getDate().toString().padStart(2, '0')}`;
      }
    }

    this.setData({
      isMember,
      isPermanentMember,
      membershipStatus,
      memberExpireTime
    });
  },

  // 开通/续费会员
  upgradeMembership() {
    wx.navigateTo({
      url: '/pages/payment/membership/membership'
    });
  },

  // 显示卡密兑换弹窗
  redeemCard() {
    this.setData({
      showRedeemModal: true,
      cardCode: ''
    });
  },

  // 隐藏卡密兑换弹窗
  hideRedeemModal() {
    this.setData({
      showRedeemModal: false,
      cardCode: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 卡密输入
  onCardCodeInput(e) {
    this.setData({
      cardCode: e.detail.value
    });
  },

  // 提交卡密兑换
  async submitRedeem() {
    const { cardCode } = this.data;
    
    if (!cardCode || cardCode.length !== 16) {
      wx.showToast({
        title: '请输入16位卡密',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '兑换中...' });

    try {
      const cardApi = require('../../../utils/api/cardApi.js');
      const result = await cardApi.redeemCard(cardCode);

      wx.hideLoading();

      if (result.success) {
        wx.showToast({
          title: '兑换成功',
          icon: 'success'
        });

        // 更新会员状态
        app.globalData.isMember = true;
        app.globalData.membershipExpiry = result.data.expiry_time;

        this.hideRedeemModal();
        this.loadMembershipInfo();
      } else {
        wx.showToast({
          title: result.message || '兑换失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('卡密兑换失败:', error);
      wx.showToast({
        title: '兑换失败，请重试',
        icon: 'none'
      });
    }
  },

  // 我的订单
  goToOrders() {
    wx.navigateTo({
      url: '/pages/user/orders/orders'
    });
  },

  // 意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/user/feedback/feedback'
    });
  },

  // 关于我们
  goToAbout() {
    wx.navigateTo({
      url: '/pages/user/about/about'
    });
  }
});

